package marketing_vo

// PetPrizePageReq 奖品分页查询请求
type PetPrizePageReq struct {
	UserId          string `json:"user_id" query:"user_id:eq"`                // 获奖人用户id
	PrizeType       int    `json:"prize_type" query:"prize_type:eq"`          // 获奖类型 1-排名奖 2-投票奖 3-创作奖
	WorkCode        string `json:"work_code" query:"work_code:eq"`            // 获奖作品编号
	ReceiveStatus   int    `json:"receive_status" query:"receive_status:eq"`  // 领奖状态 1待领取 2已领取 3已核销
	Nickname        string `json:"nick_name" query:"nick_name:eq"`            // 获奖人昵称
	EnMobile        string `json:"en_mobile" query:"en_mobile:eq"`            // 获奖人手机号
	IsExport        int    `json:"is_export" `                                // 是否导出
	CreateTimeStart string `json:"create_time_start" query:"create_time:gte"` // 创建时间开始
	CreateTimeEnd   string `json:"create_time_end" query:"create_time:lte"`   // 创建时间结束
	PageIndex       int    `json:"page_index"`                                // 页码
	PageSize        int    `json:"page_size"`                                 // 每页大小
}

// PetPrizeResp 奖品响应结构
type PetPrizeResp struct {
	Id               int64  `json:"id"`                 // 主键
	UserId           string `json:"user_id"`            // 获奖人用户id
	NickName         string `json:"nick_name"`          // 获奖人昵称
	Mobile           string `json:"mobile"`             // 获奖人手机号
	EnMobile         string `json:"en_mobile"`          // 获奖人手机号
	PrizeType        int    `json:"prize_type"`         // 获奖类型 1-排名奖 2-投票奖 3-创作奖
	WorkCode         string `json:"work_code"`          // 获奖作品编号
	PrizeCount       int    `json:"prize_count"`        // 获奖票数
	ReceiveStatus    int    `json:"receive_status"`     // 领奖状态 1待领取 2已领取 3已核销
	PrizeContent     string `json:"prize_content"`      // 奖励内容
	CouponCode       string `json:"coupon_code"`        // 优惠券码
	OrderSn          string `json:"order_sn"`           // 核销订单号
	Address          string `json:"address"`            // 收货地址
	Receiver         string `json:"receiver"`           // 收货人
	ReceiverMobile   string `json:"receiver_mobile"`    // 收货人手机号带*
	ReceiverEnMobile string `json:"receiver_en_mobile"` // 收货人加密手机号
	ReceiveTime      string `json:"receive_time"`       // 领取时间
	VerifyTime       string `json:"verify_time"`        // 核销时间
	CreateTime       string `json:"create_time"`        // 创建时间
	UpdateTime       string `json:"update_time"`        // 更新时间
}

type PetPrizePopReq struct {
	UserId string `json:"user_id"` // 用户ID
}

type PetPrizePopResp struct {
	CouponCode     string `json:"coupon_code"`      // 优惠券码
	PrizeType      int    `json:"prize_type"`       // 获奖类型 1-排名奖 2-投票奖 3-创作奖
	PrizeCount     int    `json:"prize_count"`      // 获奖票数
	VoucherPrice   int    `json:"voucher_price"`    // 优惠券金额，单位元
	VoucherEndDate string `json:"voucher_end_date"` // 优惠券到期时间
}

// PetPrizeReceiveReq 奖品领取请求
type PetPrizeReceiveReq struct {
	// 奖品ID
	PrizeId int `json:"prize_id"`
	// 用户ID
	UserId string `json:"user_id"`
	// 收货人
	Receiver string `json:"receiver"`
	// 收货人手机号
	ReceiverMobile string `json:"receiver_mobile"`
	// 详细地址
	DetailAddress string `json:"detail_address"`
}

// PetPrizeReceiveResp 奖品领取响应
type PetPrizeReceiveResp struct {
	Code    int    `json:"code"`    // 状态码
	Message string `json:"message"` // 消息
}

// PetPrizeDetailReq 奖品详情请求
type PetPrizeDetailReq struct {
	// 用户ID
	UserId string `json:"user_id"`
}

// PetPrizeDetailResp 奖品详情响应
type PetPrizeDetailResp struct {
	// 获奖类型 1-排名奖 2-投票奖 3-创作奖
	PrizeType int `json:"prize_type"`
	// 奖励内容
	PrizeContent string `json:"prize_content"`
	// 领奖状态 1待领取(获得资格) 2已领取(已提交地址) 3已核销
	ReceiveStatus int `json:"receive_status"`
	// 奖品id
	PrizeId int `json:"prize_id"`
}
